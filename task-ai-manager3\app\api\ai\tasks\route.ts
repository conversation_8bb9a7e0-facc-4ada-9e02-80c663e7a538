import { NextResponse } from 'next/server';

// Функция для генерации ответа ИИ на основе промпта и контекста задач
function generateTaskAIResponse(prompt: string, context: any): string {
  const promptLower = prompt.toLowerCase();
  
  // Базовые ответы на основе ключевых слов
  if (promptLower.includes('приоритет') || promptLower.includes('priority')) {
    return `Рекомендации по приоритизации задач:
1. Высокий приоритет: задачи, блокирующие работу других участников или критичные для проекта
2. Средний приоритет: важные задачи, но не блокирующие другие работы
3. Низкий приоритет: задачи, которые можно отложить без серьезных последствий

Анализ ваших текущих задач:
${context.tasks.filter(t => t.status === 'todo').slice(0, 3).map((task, index) => 
  `- "${task.title}": рекомендуемый приоритет - ${['Высокий', 'Средний', 'Низкий'][Math.floor(Math.random() * 3)]}`
).join('\n')}`;
  } 
  
  if (promptLower.includes('оценка') || promptLower.includes('estimate')) {
    return `Рекомендации по оценке времени для задач:
1. Разбивайте сложные задачи на подзадачи
2. Учитывайте время на коммуникацию и координацию
3. Добавляйте буфер 20-30% для непредвиденных сложностей

Оценка времени для ваших текущих задач:
${context.tasks.filter(t => t.status === 'todo').slice(0, 3).map((task, index) => 
  `- "${task.title}": примерно ${Math.floor(Math.random() * 5) + 1}-${Math.floor(Math.random() * 5) + 6} часов`
).join('\n')}`;
  }
  
  if (promptLower.includes('распределение') || promptLower.includes('assign')) {
    const assignees = ['Алексей', 'Мария', 'Иван', 'Елена', 'Дмитрий'];
    return `Рекомендации по распределению задач:
1. Учитывайте навыки и опыт каждого участника
2. Равномерно распределяйте нагрузку
3. Избегайте назначения слишком многих задач одному человеку

Предлагаемое распределение для ваших текущих задач:
${context.tasks.filter(t => t.status === 'todo').slice(0, 3).map((task, index) => 
  `- "${task.title}": рекомендуемый исполнитель - ${assignees[Math.floor(Math.random() * assignees.length)]}`
).join('\n')}`;
  }
  
  if (promptLower.includes('зависимости') || promptLower.includes('dependencies')) {
    return `Анализ зависимостей между задачами:
1. Выявляйте задачи, которые должны быть выполнены последовательно
2. Определяйте задачи, которые можно выполнять параллельно
3. Документируйте зависимости для лучшего планирования

Выявленные зависимости в ваших задачах:
${context.tasks.filter(t => t.status === 'todo').slice(0, 3).map((task, index) => 
  `- "${task.title}" должна быть выполнена ${index === 0 ? 'в первую очередь' : `после "${context.tasks.filter(t => t.status === 'todo')[index-1].title}"`}`
).join('\n')}`;
  }
  
  if (promptLower.includes('прогресс') || promptLower.includes('progress')) {
    const todoCount = context.tasks.filter(t => t.status === 'todo').length;
    const inProgressCount = context.tasks.filter(t => t.status === 'inProgress').length;
    const doneCount = context.tasks.filter(t => t.status === 'done').length;
    const totalCount = todoCount + inProgressCount + doneCount;
    
    const progressPercentage = totalCount > 0 ? Math.round((doneCount / totalCount) * 100) : 0;
    
    return `Анализ прогресса выполнения задач:
1. Выполнено: ${doneCount} задач (${progressPercentage}%)
2. В процессе: ${inProgressCount} задач (${totalCount > 0 ? Math.round((inProgressCount / totalCount) * 100) : 0}%)
3. Ожидает выполнения: ${todoCount} задач (${totalCount > 0 ? Math.round((todoCount / totalCount) * 100) : 0}%)

${progressPercentage < 30 ? 'Проект находится на начальной стадии. Рекомендую сосредоточиться на запуске ключевых задач.' : 
  progressPercentage < 70 ? 'Проект в активной фазе. Рекомендую регулярно отслеживать прогресс и решать возникающие блокеры.' : 
  'Проект близок к завершению. Рекомендую сосредоточиться на тестировании и документации.'}`;
  }
  
  if (promptLower.includes('оптимизация') || promptLower.includes('optimize')) {
    return `Рекомендации по оптимизации работы с задачами:
1. Группируйте похожие задачи и выполняйте их последовательно
2. Выделяйте блоки времени для работы без прерываний
3. Используйте технику "помидора" (25 минут работы, 5 минут отдыха)
4. Начинайте день с самых сложных или важных задач
5. Регулярно анализируйте и корректируйте свой подход к работе`;
  }
  
  if (promptLower.includes('генерировать') || promptLower.includes('generate')) {
    const taskTypes = [
      'Исследование рынка',
      'Анализ конкурентов',
      'Разработка прототипа',
      'Тестирование функциональности',
      'Оптимизация производительности',
      'Создание документации',
      'Подготовка презентации',
      'Обучение пользователей',
      'Сбор обратной связи',
      'Анализ метрик'
    ];
    
    return `Предлагаемые новые задачи для вашего проекта:
${Array.from({length: 5}, (_, i) => `${i+1}. ${taskTypes[Math.floor(Math.random() * taskTypes.length)]}`).join('\n')}

Рекомендую добавить эти задачи в ваш проект и назначить ответственных исполнителей.`;
  }
  
  // Общий ответ, если не найдено соответствий
  return `Я могу помочь с управлением задачами. Пожалуйста, уточните ваш вопрос или запрос. Вы можете спросить о приоритизации, оценке времени, распределении задач, зависимостях между задачами, анализе прогресса или оптимизации работы.`;
}

export async function POST(request: Request) {
  try {
    // Получаем данные из запроса
    const data = await request.json();
    const { prompt, context } = data;
    
    if (!prompt) {
      return NextResponse.json(
        { error: 'Prompt is required' },
        { status: 400 }
      );
    }
    
    if (!context) {
      return NextResponse.json(
        { error: 'Context is required' },
        { status: 400 }
      );
    }
    
    // Генерируем ответ ИИ
    const response = generateTaskAIResponse(prompt, context);
    
    // Возвращаем ответ
    return NextResponse.json({ response });
  } catch (error) {
    console.error('Error in AI tasks route:', error);
    return NextResponse.json(
      { error: 'Failed to process AI request' },
      { status: 500 }
    );
  }
}
