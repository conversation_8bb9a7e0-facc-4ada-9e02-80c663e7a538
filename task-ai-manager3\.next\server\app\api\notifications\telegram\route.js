/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/notifications/telegram/route";
exports.ids = ["app/api/notifications/telegram/route"];
exports.modules = {

/***/ "(rsc)/./app/api/notifications/telegram/route.ts":
/*!*************************************************!*\
  !*** ./app/api/notifications/telegram/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n// Обработчик POST-запроса для отправки Telegram-уведомлений\nasync function POST(request) {\n    try {\n        // Получаем данные из запроса\n        const data = await request.json();\n        // Проверяем наличие необходимых данных\n        if (!data.telegramUsername || !data.message) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Missing required fields'\n            }, {\n                status: 400\n            });\n        }\n        // В реальном приложении здесь была бы отправка сообщения через Telegram Bot API\n        // Для демонстрации просто логируем данные\n        console.log('Telegram notification data:', data);\n        // Имитируем успешную отправку\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: `Telegram notification sent to @${data.telegramUsername}`\n        });\n    /* \n    // Пример реальной отправки сообщения через Telegram Bot API\n    const botToken = process.env.TELEGRAM_BOT_TOKEN;\n    if (!botToken) {\n      throw new Error('TELEGRAM_BOT_TOKEN is not defined');\n    }\n    \n    // Формируем текст сообщения\n    const messageText = `\n📬 *${data.subject}*\n\n${data.message}\n\n*Сообщение:* ${data.messageText}\n*От:* ${data.senderName}\n*Чат:* ${data.chatName}\n    `;\n    \n    // Отправляем запрос к Telegram Bot API\n    const response = await fetch(`https://api.telegram.org/bot${botToken}/sendMessage`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        chat_id: data.telegramUsername,\n        text: messageText,\n        parse_mode: 'Markdown',\n      }),\n    });\n    \n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(`Telegram API error: ${JSON.stringify(errorData)}`);\n    }\n    \n    const responseData = await response.json();\n    \n    return NextResponse.json({ \n      success: true, \n      message: `Telegram notification sent to @${data.telegramUsername}`,\n      telegramResponse: responseData\n    });\n    */ } catch (error) {\n        console.error('Error sending Telegram notification:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'Failed to send Telegram notification',\n            error: String(error)\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/notifications/telegram/route.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnotifications%2Ftelegram%2Froute&page=%2Fapi%2Fnotifications%2Ftelegram%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnotifications%2Ftelegram%2Froute.ts&appDir=D%3A%5CProjects%5Ctask-ai-manager3%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjects%5Ctask-ai-manager3&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnotifications%2Ftelegram%2Froute&page=%2Fapi%2Fnotifications%2Ftelegram%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnotifications%2Ftelegram%2Froute.ts&appDir=D%3A%5CProjects%5Ctask-ai-manager3%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjects%5Ctask-ai-manager3&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Projects_task_ai_manager3_app_api_notifications_telegram_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/notifications/telegram/route.ts */ \"(rsc)/./app/api/notifications/telegram/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/notifications/telegram/route\",\n        pathname: \"/api/notifications/telegram\",\n        filename: \"route\",\n        bundlePath: \"app/api/notifications/telegram/route\"\n    },\n    resolvedPagePath: \"D:\\\\Projects\\\\task-ai-manager3\\\\app\\\\api\\\\notifications\\\\telegram\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Projects_task_ai_manager3_app_api_notifications_telegram_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnotifications%2Ftelegram%2Froute&page=%2Fapi%2Fnotifications%2Ftelegram%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnotifications%2Ftelegram%2Froute.ts&appDir=D%3A%5CProjects%5Ctask-ai-manager3%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjects%5Ctask-ai-manager3&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnotifications%2Ftelegram%2Froute&page=%2Fapi%2Fnotifications%2Ftelegram%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnotifications%2Ftelegram%2Froute.ts&appDir=D%3A%5CProjects%5Ctask-ai-manager3%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjects%5Ctask-ai-manager3&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();