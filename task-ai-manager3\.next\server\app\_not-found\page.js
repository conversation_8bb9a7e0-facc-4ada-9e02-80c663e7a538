/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"1de822cf709b\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZWN0c1xcdGFzay1haS1tYW5hZ2VyM1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjFkZTgyMmNmNzA5YlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_cyrillic_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\",\"cyrillic\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\",\\\"cyrillic\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_cyrillic_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_cyrillic_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_jetbrains_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"JetBrains_Mono\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-jetbrains\"}],\"variableName\":\"jetbrainsMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"JetBrains_Mono\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-jetbrains\\\"}],\\\"variableName\\\":\\\"jetbrainsMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_jetbrains_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_jetbrains_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/theme-provider */ \"(rsc)/./components/theme-provider.tsx\");\n/* harmony import */ var _lib_translations__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/translations */ \"(rsc)/./lib/translations.tsx\");\n/* harmony import */ var _lib_socket_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/socket-context */ \"(rsc)/./lib/socket-context.tsx\");\n\n\n\n\n\n\n // Import SocketProvider\nconst metadata = {\n    title: \"Minimumist - AI Task Manager\",\n    description: \"Minimalist task management app with AI assistant\",\n    generator: 'v0.dev'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_cyrillic_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().variable)} ${(next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_jetbrains_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_6___default().variable)} font-sans`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_socket_context__WEBPACK_IMPORTED_MODULE_4__.SocketProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_translations__WEBPACK_IMPORTED_MODULE_3__.TranslationProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_provider__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                        attribute: \"class\",\n                        defaultTheme: \"system\",\n                        enableSystem: true,\n                        disableTransitionOnChange: true,\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\task-ai-manager3\\\\app\\\\layout.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\task-ai-manager3\\\\app\\\\layout.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\task-ai-manager3\\\\app\\\\layout.tsx\",\n                lineNumber: 34,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\task-ai-manager3\\\\app\\\\layout.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\task-ai-manager3\\\\app\\\\layout.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ThemeProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projects\\task-ai-manager3\\components\\theme-provider.tsx",
"ThemeProvider",
);

/***/ }),

/***/ "(rsc)/./lib/socket-context.tsx":
/*!********************************!*\
  !*** ./lib/socket-context.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SocketProvider: () => (/* binding */ SocketProvider),
/* harmony export */   useSocket: () => (/* binding */ useSocket)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const useSocket = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useSocket() from the server but useSocket is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projects\\task-ai-manager3\\lib\\socket-context.tsx",
"useSocket",
);const SocketProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SocketProvider() from the server but SocketProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projects\\task-ai-manager3\\lib\\socket-context.tsx",
"SocketProvider",
);

/***/ }),

/***/ "(rsc)/./lib/translations.tsx":
/*!******************************!*\
  !*** ./lib/translations.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   TranslationProvider: () => (/* binding */ TranslationProvider),
/* harmony export */   useTranslation: () => (/* binding */ useTranslation)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const TranslationProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call TranslationProvider() from the server but TranslationProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projects\\task-ai-manager3\\lib\\translations.tsx",
"TranslationProvider",
);const useTranslation = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useTranslation() from the server but useTranslation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projects\\task-ai-manager3\\lib\\translations.tsx",
"useTranslation",
);

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5CProjects%5Ctask-ai-manager3%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjects%5Ctask-ai-manager3&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5CProjects%5Ctask-ai-manager3%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjects%5Ctask-ai-manager3&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst notFound0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                notFound0,\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [module1, \"D:\\\\Projects\\\\task-ai-manager3\\\\app\\\\layout.tsx\"],\n'not-found': [module2, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5CProjects%5Ctask-ai-manager3%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjects%5Ctask-ai-manager3&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Clib%5C%5Csocket-context.tsx%22%2C%22ids%22%3A%5B%22SocketProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Clib%5C%5Ctranslations.tsx%22%2C%22ids%22%3A%5B%22TranslationProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%2C%5C%22cyrillic%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-jetbrains%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Clib%5C%5Csocket-context.tsx%22%2C%22ids%22%3A%5B%22SocketProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Clib%5C%5Ctranslations.tsx%22%2C%22ids%22%3A%5B%22TranslationProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%2C%5C%22cyrillic%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-jetbrains%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(rsc)/./components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./lib/socket-context.tsx */ \"(rsc)/./lib/socket-context.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./lib/translations.tsx */ \"(rsc)/./lib/translations.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Clib%5C%5Csocket-context.tsx%22%2C%22ids%22%3A%5B%22SocketProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Clib%5C%5Ctranslations.tsx%22%2C%22ids%22%3A%5B%22TranslationProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%2C%5C%22cyrillic%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-jetbrains%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0cyU1QyU1Q3Rhc2stYWktbWFuYWdlcjMlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjbGllbnQtcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDUHJvamVjdHMlNUMlNUN0YXNrLWFpLW1hbmFnZXIzJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDY2xpZW50LXNlZ21lbnQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q1Byb2plY3RzJTVDJTVDdGFzay1haS1tYW5hZ2VyMyU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0cyU1QyU1Q3Rhc2stYWktbWFuYWdlcjMlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNodHRwLWFjY2Vzcy1mYWxsYmFjayU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0cyU1QyU1Q3Rhc2stYWktbWFuYWdlcjMlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNsYXlvdXQtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0cyU1QyU1Q3Rhc2stYWktbWFuYWdlcjMlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q2FzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0cyU1QyU1Q3Rhc2stYWktbWFuYWdlcjMlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q21ldGFkYXRhLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0cyU1QyU1Q3Rhc2stYWktbWFuYWdlcjMlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvT0FBaUk7QUFDakk7QUFDQSwwT0FBb0k7QUFDcEk7QUFDQSwwT0FBb0k7QUFDcEk7QUFDQSxvUkFBMEo7QUFDMUo7QUFDQSx3T0FBbUk7QUFDbkk7QUFDQSw0UEFBOEk7QUFDOUk7QUFDQSxrUUFBaUo7QUFDako7QUFDQSxzUUFBa0oiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFByb2plY3RzXFxcXHRhc2stYWktbWFuYWdlcjNcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxjbGllbnQtcGFnZS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcUHJvamVjdHNcXFxcdGFzay1haS1tYW5hZ2VyM1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGNsaWVudC1zZWdtZW50LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxQcm9qZWN0c1xcXFx0YXNrLWFpLW1hbmFnZXIzXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFByb2plY3RzXFxcXHRhc2stYWktbWFuYWdlcjNcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxodHRwLWFjY2Vzcy1mYWxsYmFja1xcXFxlcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcUHJvamVjdHNcXFxcdGFzay1haS1tYW5hZ2VyM1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGxheW91dC1yb3V0ZXIuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFByb2plY3RzXFxcXHRhc2stYWktbWFuYWdlcjNcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxtZXRhZGF0YVxcXFxhc3luYy1tZXRhZGF0YS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcUHJvamVjdHNcXFxcdGFzay1haS1tYW5hZ2VyM1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXG1ldGFkYXRhXFxcXG1ldGFkYXRhLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxQcm9qZWN0c1xcXFx0YXNrLWFpLW1hbmFnZXIzXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxccmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\task-ai-manager3\\\\components\\\\theme-provider.tsx\",\n        lineNumber: 10,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3RoZW1lLXByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRThCO0FBSVY7QUFFYixTQUFTQyxjQUFjLEVBQUVFLFFBQVEsRUFBRSxHQUFHQyxPQUEyQjtJQUN0RSxxQkFBTyw4REFBQ0Ysc0RBQWtCQTtRQUFFLEdBQUdFLEtBQUs7a0JBQUdEOzs7Ozs7QUFDekMiLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZWN0c1xcdGFzay1haS1tYW5hZ2VyM1xcY29tcG9uZW50c1xcdGhlbWUtcHJvdmlkZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCdcbmltcG9ydCB7XG4gIFRoZW1lUHJvdmlkZXIgYXMgTmV4dFRoZW1lc1Byb3ZpZGVyLFxuICB0eXBlIFRoZW1lUHJvdmlkZXJQcm9wcyxcbn0gZnJvbSAnbmV4dC10aGVtZXMnXG5cbmV4cG9ydCBmdW5jdGlvbiBUaGVtZVByb3ZpZGVyKHsgY2hpbGRyZW4sIC4uLnByb3BzIH06IFRoZW1lUHJvdmlkZXJQcm9wcykge1xuICByZXR1cm4gPE5leHRUaGVtZXNQcm92aWRlciB7Li4ucHJvcHN9PntjaGlsZHJlbn08L05leHRUaGVtZXNQcm92aWRlcj5cbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlRoZW1lUHJvdmlkZXIiLCJOZXh0VGhlbWVzUHJvdmlkZXIiLCJjaGlsZHJlbiIsInByb3BzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/socket-context.tsx":
/*!********************************!*\
  !*** ./lib/socket-context.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SocketProvider: () => (/* binding */ SocketProvider),\n/* harmony export */   useSocket: () => (/* binding */ useSocket)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! socket.io-client */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/index.js\");\n/* __next_internal_client_entry_do_not_use__ useSocket,SocketProvider auto */ \n\n\n// Определяем URL нашего сервера чата\n// Используем переменную окружения или значение по умолчанию\nconst SOCKET_URL = process.env.NEXT_PUBLIC_CHAT_SERVER_URL || 'http://localhost:3003';\nconst SocketContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    socket: null,\n    isConnected: false\n});\n// Хук для удобного доступа к контексту\nconst useSocket = ()=>{\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SocketContext);\n};\nconst SocketProvider = ({ children })=>{\n    console.log('--- SocketProvider component rendering ---'); // Add this log\n    const [socket, setSocket] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SocketProvider.useEffect\": ()=>{\n            console.log('SocketProvider useEffect running. Creating socket instance...');\n            let socketInstance = null;\n            // Определяем обработчики вне try...catch, чтобы они были доступны в cleanup\n            const onConnect = {\n                \"SocketProvider.useEffect.onConnect\": ()=>{\n                    // Используем socketInstance из замыкания\n                    if (socketInstance) {\n                        console.log('Socket connected:', socketInstance.id);\n                        setIsConnected(true);\n                    }\n                }\n            }[\"SocketProvider.useEffect.onConnect\"];\n            const onDisconnect = {\n                \"SocketProvider.useEffect.onDisconnect\": (reason)=>{\n                    console.log('Socket disconnected:', reason);\n                    setIsConnected(false);\n                // Можно добавить логику обработки отключения, например, попытку переподключения\n                }\n            }[\"SocketProvider.useEffect.onDisconnect\"];\n            const onConnectError = {\n                \"SocketProvider.useEffect.onConnectError\": (error)=>{\n                    console.error('Socket connection error:', error);\n                    setIsConnected(false);\n                }\n            }[\"SocketProvider.useEffect.onConnectError\"];\n            try {\n                console.log('Attempting to create socket instance...');\n                // Создаем экземпляр сокета при монтировании компонента\n                // Опция `autoConnect: false` предотвращает автоматическое подключение\n                // Мы можем подключиться вручную позже, например, после аутентификации пользователя\n                socketInstance = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_2__.io)(SOCKET_URL, {\n                    autoConnect: true,\n                    reconnectionAttempts: 5,\n                    transports: [\n                        'websocket'\n                    ]\n                });\n                setSocket(socketInstance);\n                console.log('Socket instance created:', socketInstance ? 'Yes' : 'No');\n                // Добавляем обработчики, если сокет успешно создан\n                socketInstance.on('connect', onConnect);\n                socketInstance.on('disconnect', onDisconnect);\n                socketInstance.on('connect_error', onConnectError);\n            } catch (error) {\n                console.error('Error creating socket instance:', error);\n                setIsConnected(false); // Убедимся, что статус disconnected\n            }\n            // Очистка при размонтировании компонента\n            return ({\n                \"SocketProvider.useEffect\": ()=>{\n                    if (socketInstance) {\n                        console.log('Disconnecting socket...');\n                        socketInstance.off('connect', onConnect);\n                        socketInstance.off('disconnect', onDisconnect);\n                        socketInstance.off('connect_error', onConnectError);\n                        socketInstance.disconnect();\n                        setSocket(null);\n                        setIsConnected(false);\n                    }\n                }\n            })[\"SocketProvider.useEffect\"];\n        }\n    }[\"SocketProvider.useEffect\"], []); // Пустой массив зависимостей гарантирует выполнение только один раз\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SocketContext.Provider, {\n        value: {\n            socket,\n            isConnected\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\task-ai-manager3\\\\lib\\\\socket-context.tsx\",\n        lineNumber: 99,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/socket-context.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/translations.tsx":
/*!******************************!*\
  !*** ./lib/translations.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TranslationProvider: () => (/* binding */ TranslationProvider),\n/* harmony export */   useTranslation: () => (/* binding */ useTranslation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ TranslationProvider,useTranslation auto */ \n\n// Define the available languages\nconst languages = {\n    en: {\n        // General\n        dashboard: \"Dashboard\",\n        projects: \"Projects\",\n        analytics: \"Analytics\",\n        settings: \"Settings\",\n        tasks: \"Tasks\",\n        documents: \"Documents\",\n        save: \"Save\",\n        cancel: \"Cancel\",\n        calendar: \"Calendar\",\n        // Tasks\n        toDo: \"To Do\",\n        inProgress: \"In Progress\",\n        done: \"Done\",\n        addTask: \"Add Task\",\n        deleteTask: \"Delete Task\",\n        editTask: \"Edit Task\",\n        taskTitle: \"Task Title\",\n        taskDescription: \"Task Description\",\n        taskPriority: \"Priority\",\n        high: \"High\",\n        medium: \"Medium\",\n        low: \"Low\",\n        dueDate: \"Due Date\",\n        selectPriority: \"Select priority\",\n        taskAge: \"Task Age\",\n        daysLeft: \" days left\",\n        daysOverdue: \" days overdue\",\n        taskUpdated: \"Task Updated\",\n        taskUpdatedSuccess: \"Task has been successfully updated\",\n        taskDeleted: \"Task Deleted\",\n        taskDeletedSuccess: \"Task has been successfully deleted\",\n        taskCreated: \"Task Created\",\n        taskCreatedSuccess: \"Task has been successfully created\",\n        deleteTaskConfirmation: \"Are you sure you want to delete task {title}?\",\n        delete: \"Delete\",\n        close: \"Close\",\n        editTaskDescription: \"Edit the task details\",\n        viewMode: \"View mode\",\n        kanban: \"Kanban\",\n        list: \"List\",\n        searchTasks: \"Search tasks\",\n        priority: \"Priority\",\n        allPriorities: \"All priorities\",\n        assignee: \"Assignee\",\n        allAssignees: \"All assignees\",\n        noTasksFound: \"No tasks found\",\n        add: \"Add\",\n        commaSeparatedTags: \"Comma separated tags\",\n        // Subtasks\n        manageSubtasks: \"Manage Subtasks\",\n        manageSubtasksDescription: \"Add or manage subtasks for this task\",\n        subtasks: \"Subtasks\",\n        newSubtask: \"New Subtask\",\n        newSubtaskPlaceholder: \"Enter subtask name\",\n        extractSubtasks: \"Extract as Subtasks\",\n        noSubtasksFound: \"No Subtasks Found\",\n        noSubtasksFoundDescription: \"Could not extract subtasks from AI response\",\n        subtasksAdded: \"Subtasks Added\",\n        subtasksAddedFromAI: \"Added {count} subtasks from AI suggestion\",\n        generateSubtasksWithAI: \"Generate Subtasks with AI\",\n        // AI Assistant\n        aiAssist: \"AI Assistant\",\n        aiAssistDescription: \"Get AI help with this task\",\n        aiPrompt: \"Ask AI\",\n        aiPromptPlaceholder: \"e.g. Help me break down this task\",\n        aiPromptExample1: \"Break down this task\",\n        aiPromptExample2: \"Suggest a priority\",\n        aiPromptExample3: \"Recommend next steps\",\n        aiResponse: \"AI Response\",\n        generateResponse: \"Generate\",\n        thinking: \"Thinking...\",\n        currentTask: \"Current Task\",\n        // Projects\n        projectProgress: \"Project Progress\",\n        createProject: \"Create Project\",\n        editProject: \"Edit Project\",\n        deleteProject: \"Delete Project\",\n        projectName: \"Project Name\",\n        projectDescription: \"Project Description\",\n        selectProject: \"Select Project\",\n        newProject: \"New Project\",\n        projectCreated: \"Project Created\",\n        projectCreatedDescription: \"Project has been created successfully\",\n        projectUpdated: \"Project Updated\",\n        projectUpdatedDescription: \"Project has been updated successfully\",\n        projectDeleted: \"Project Deleted\",\n        projectDeletedDescription: \"Project has been deleted successfully\",\n        deleteProjectConfirmation: \"Are you sure you want to delete project {name}?\",\n        projectCompletion: \"Project Completion\",\n        aiProjectAssistant: \"AI Project Assistant\",\n        askAI: \"Ask AI\",\n        applyAISuggestions: \"Apply AI Suggestions\",\n        aiSuggestionsApplied: \"AI Suggestions Applied\",\n        projectUpdatedWithAI: \"Project has been updated with AI suggestions\",\n        privateProject: \"Private Project\",\n        privateProjectDescription: \"Private projects are only visible to you and people you share them with\",\n        private: \"Private\",\n        shared: \"Shared\",\n        shareProject: \"Share Project\",\n        currentParticipants: \"Current Participants\",\n        addParticipants: \"Add Participants\",\n        searchTeamMembers: \"Search team members\",\n        noResultsFound: \"No results found\",\n        selectedParticipants: \"Selected Participants\",\n        share: \"Share\",\n        projectShared: \"Project Shared\",\n        projectSharedDescription: \"Project has been shared with {count} participants\",\n        participantRemoved: \"Participant Removed\",\n        participantRemovedDescription: \"Participant has been removed from the project\",\n        totalProjects: \"Total Projects\",\n        completedProjects: \"Completed Projects\",\n        inProgressProjects: \"In Progress\",\n        averageCompletion: \"Average Completion\",\n        noProjects: \"No Projects\",\n        createFirstProject: \"Create your first project to get started with task management\",\n        participants: \"Participants\",\n        noParticipants: \"No participants\",\n        actions: \"Actions\",\n        completion: \"Completion\",\n        createdAt: \"Created At\",\n        // Documents\n        projectDocuments: \"Project Documents\",\n        uploadDocument: \"Upload Document\",\n        addDocumentLink: \"Add Document Link\",\n        viewDocuments: \"View Documents\",\n        noDocuments: \"No documents for this project\",\n        documentName: \"Document Name\",\n        documentType: \"Document Type\",\n        documentURL: \"Document URL\",\n        addDocument: \"Add Document\",\n        addDocumentDescription: \"Add a document link to the current project\",\n        documentUploaded: \"Document Uploaded\",\n        documentUploadedSuccess: \"Document {name} has been uploaded successfully\",\n        documentAdded: \"Document Added\",\n        documentAddedSuccess: \"Document {name} has been added successfully\",\n        editDocument: \"Edit Document\",\n        deleteDocument: \"Delete Document\",\n        deleteDocumentConfirmation: \"Are you sure you want to delete document {name}?\",\n        documentDeleted: \"Document Deleted\",\n        documentDeletedSuccessfully: \"Document has been deleted successfully\",\n        documentUpdated: \"Document Updated\",\n        documentUpdatedSuccessfully: \"Document {name} has been updated successfully\",\n        folders: \"Folders\",\n        files: \"Files\",\n        newFolder: \"New Folder\",\n        uploadFiles: \"Upload Files\",\n        uploadFilesDescription: \"Upload files to the current project\",\n        dropFilesHere: \"Drop files here\",\n        or: \"or\",\n        browseFiles: \"Browse Files\",\n        selectedFiles: \"Selected Files\",\n        uploading: \"Uploading...\",\n        upload: \"Upload\",\n        uploadComplete: \"Upload Complete\",\n        filesUploadedSuccessfully: \"{count} files uploaded successfully\",\n        createdBy: \"Created by\",\n        lastModified: \"Last modified\",\n        size: \"Size\",\n        modified: \"Modified\",\n        folder: \"Folder\",\n        noFolder: \"No folder\",\n        tags: \"Tags\",\n        content: \"Content\",\n        aiDocumentAssistant: \"AI Document Assistant\",\n        aiDocumentPromptPlaceholder: \"e.g. Improve this document, Summarize, Suggest tags\",\n        aiDocumentPromptSuggestion1: \"Improve this document\",\n        aiDocumentPromptSuggestion2: \"Summarize content\",\n        aiDocumentPromptSuggestion3: \"Suggest tags\",\n        currentDocument: \"Current Document\",\n        documentUpdatedWithAI: \"Document has been updated with AI suggestions\",\n        documentAIUpdated: \"Document {name} has been updated with AI suggestions\",\n        openMenu: \"Open Menu\",\n        view: \"View\",\n        edit: \"Edit\",\n        download: \"Download\",\n        totalDocuments: \"Total Documents\",\n        totalSize: \"Total Size\",\n        latestUpdate: \"Latest Update\",\n        documentTypes: \"Document Types\",\n        groupByType: \"Group by Type\",\n        version: \"Version\",\n        status: \"Status\",\n        description: \"Description\",\n        // Admin\n        adminDashboard: \"Admin Dashboard\",\n        userManagement: \"User Management\",\n        blockUser: \"Block User\",\n        unblockUser: \"Unblock User\",\n        deleteUser: \"Delete User\",\n        confirmDelete: \"Are you sure you want to delete this user?\",\n        confirmBlock: \"Are you sure you want to block this user?\",\n        confirmUnblock: \"Are you sure you want to unblock this user?\",\n        username: \"Username\",\n        email: \"Email\",\n        active: \"Active\",\n        blocked: \"Blocked\",\n        // Settings\n        settingsSaved: \"Settings Saved\",\n        generalSettingsSaved: \"General settings have been saved successfully\",\n        aiSettingsSaved: \"AI settings have been saved successfully\",\n        emailSettingsSaved: \"Email settings have been saved successfully\",\n        aiModelApiKey: \"AI Model API Key\",\n        enterApiKeyPlaceholder: \"Enter your API key\",\n        // Admin Settings specific keys\n        generalSettings: \"General Settings\",\n        aiSettings: \"AI Settings\",\n        emailSettings: \"Email Settings\",\n        generalSettingsDescription: \"Configure general site settings\",\n        aiSettingsDescription: \"Configure AI assistant settings\",\n        emailSettingsDescription: \"Configure email notification settings\",\n        siteName: \"Site Name\",\n        siteDescription: \"Site Description\",\n        allowRegistration: \"Allow User Registration\",\n        requireEmailVerification: \"Require Email Verification\",\n        maxProjectsPerUser: \"Max Projects Per User\",\n        maxTasksPerProject: \"Max Tasks Per Project\",\n        saveSettings: \"Save Settings\",\n        enableAI: \"Enable AI Assistant\",\n        aiModel: \"AI Model\",\n        aiTemperature: \"AI Temperature\",\n        maxTokens: \"Max Tokens\",\n        customPrompt: \"Custom System Prompt\",\n        aiWarning: \"AI Usage Warning\",\n        aiWarningDescription: \"Inform users about potential AI inaccuracies or costs\",\n        enableEmailNotifications: \"Enable Email Notifications\",\n        smtpServer: \"SMTP Server\",\n        smtpPort: \"SMTP Port\",\n        smtpUsername: \"SMTP Username\",\n        smtpPassword: \"SMTP Password\",\n        fromEmail: \"From Email Address\",\n        systemSettings: \"System Settings\",\n        // Admin Dashboard specific keys\n        adminDashboardDescription: \"Overview of system statistics and management\",\n        totalUsers: \"Total Users\",\n        activeUsers: \"Active Users\",\n        totalTasks: \"Total Tasks\",\n        completedTasks: \"Completed Tasks\",\n        completionRate: \"Completion Rate\",\n        taskCompletionRate: \"Task Completion Rate\",\n        // Dashboard specific keys\n        logoutSuccessful: \"Logout Successful\",\n        youHaveBeenLoggedOut: \"You have been successfully logged out.\",\n        // Analytics specific keys\n        taskDistribution: \"Task Distribution\",\n        projectStatistics: \"Project Statistics\",\n        tasksByStatus: \"Tasks by Status\",\n        tasksByStatusDescription: \"Distribution of tasks across different statuses\",\n        taskCompletion: \"Task Completion Over Time\",\n        taskCompletionDescription: \"Trend of task completion\",\n        completed: \"Completed\",\n        projectsByStatus: \"Projects by Status\",\n        projectsByStatusDescription: \"Distribution of projects across different statuses\",\n        onHold: \"On Hold\",\n        monthlyProjects: \"Monthly Project Creation\",\n        monthlyProjectsDescription: \"Trend of new projects created each month\",\n        // User Analytics specific keys\n        userParticipation: \"User Participation\",\n        userActivity: \"User Activity\",\n        projectCollaboration: \"Project Collaboration\",\n        topUsersByProjects: \"Top Users by Projects\",\n        topUsersByProjectsDescription: \"Users with the most projects\",\n        projectOwnershipDistribution: \"Project Ownership Distribution\",\n        projectOwnershipDistributionDescription: \"Who owns the most projects\",\n        topUsersByTasks: \"Top Users by Tasks\",\n        topUsersByTasksDescription: \"Users with the most tasks\",\n        taskCompletionByUser: \"Task Completion by User\",\n        taskCompletionByUserDescription: \"Who completes the most tasks\",\n        projectsByTeamSize: \"Projects by Team Size\",\n        projectsByTeamSizeDescription: \"Distribution of projects by number of participants\",\n        collaborationDistribution: \"Collaboration Distribution\",\n        collaborationDistributionDescription: \"How projects are distributed by team size\",\n        totalProjects: \"Total Projects\",\n        ownedProjects: \"Owned Projects\",\n        participatedProjects: \"Participated Projects\",\n        completedTasks: \"Completed Tasks\",\n        inProgressTasks: \"In Progress Tasks\",\n        todoTasks: \"To Do Tasks\",\n        singleUserProjects: \"Single User Projects\",\n        multiUserProjects: \"{count} User Projects\",\n        // Sidebar specific keys\n        aiAssistant: \"AI Assistant\",\n        aiTaskTracker: \"AI Task Tracker\",\n        // Project timeline keys\n        projectAge: \"Project Age\",\n        daysLeft: \"Days Left\",\n        days: \"days\",\n        daysOverdue: \"days overdue\",\n        noDueDate: \"No due date\",\n        noChanges: \"No Changes\",\n        allParticipantsAlreadyAdded: \"All selected participants are already in the project\",\n        // AI Assistant\n        error: \"Error\",\n        aiRequestFailed: \"Failed to process AI request. Please try again.\",\n        // Other\n        all: \"All\",\n        spreadsheets: \"Spreadsheets\",\n        useMainMenu: \"Use the sidebar menu for navigation\",\n        pdfs: \"PDFs\",\n        other: \"Other\",\n        noDocumentsFound: \"No documents found\",\n        noDocumentsMatchingSearch: \"No documents matching search '{query}'\",\n        noDocumentsInProject: \"No documents in this project\",\n        noProjectSelected: \"No project selected\",\n        selectProjectToViewDocuments: \"Select a project to view its documents\",\n        selectProjectToManageDocuments: \"Select a project to manage its documents\",\n        documentsForProject: \"Documents for project: {name}\",\n        searchDocuments: \"Search documents\",\n        // Header\n        myAccount: \"My Account\",\n        profile: \"Profile\",\n        headerSettings: \"Settings\",\n        adminPanel: \"Admin Panel\",\n        adminRole: \"Administrator\",\n        userRole: \"User\",\n        logout: \"Logout\",\n        notifications: \"Notifications\",\n        clearAll: \"Clear All\",\n        noNotifications: \"No notifications\",\n        role: \"Role\",\n        name: \"Name\",\n        editProfile: \"Edit Profile\",\n        appearance: \"Appearance\",\n        theme: \"Theme\",\n        language: \"Language\",\n        light: \"Light\",\n        dark: \"Dark\",\n        marketingEmails: \"Marketing Emails\",\n        taskNotifications: \"Task Notifications\",\n        systemNotifications: \"System Notifications\",\n        projectNotifications: \"Project Notifications\",\n        reminderSettings: \"Reminder Settings\",\n        emailReminders: \"Email Reminders\",\n        telegramReminders: \"Telegram Reminders\",\n        reminders: \"Reminders\",\n        emailRemindersDescription: \"Receive reminders via email\",\n        telegramRemindersDescription: \"Receive reminders via Telegram\",\n        emailForReminders: \"Email for reminders\",\n        telegramUsername: \"Telegram username\",\n        emailFromProfileUsed: \"Email from your profile is used\",\n        reminderSettingsSaved: \"Reminder settings have been updated\",\n        saveChanges: \"Save Changes\",\n        settingsUpdatedSuccess: \"Settings have been updated successfully\",\n        // Project Management Specific\n        validationError: \"Validation Error\",\n        projectNameRequired: \"Project name is required.\",\n        selectProjectToUseQuickActions: \"Select a project to use quick actions.\",\n        error: \"Error\",\n        cannotNavigateWithoutProject: \"Cannot navigate to tasks without a selected project.\",\n        createNewProject: \"Create New Project\",\n        projectNamePlaceholder: \"Enter project name\",\n        projectDescriptionPlaceholder: \"Enter project description\",\n        selectAssignee: \"Select assignee\",\n        unassigned: \"Unassigned\",\n        selectDate: \"Select date\",\n        create: \"Create\",\n        viewTasks: \"View Tasks\",\n        discussProject: \"Discuss Project\",\n        discussTask: \"Discuss Task\",\n        projectDetails: \"Project Details\",\n        // status: \"Status\", // Removed duplicate - already exists under // Documents\n        created: \"Created\",\n        notSet: \"Not Set\",\n        privacy: \"Privacy\",\n        public: \"Public\",\n        todo: \"To Do\",\n        quickActions: \"Quick Actions\",\n        viewCalendar: \"View Calendar\",\n        team: \"Team\",\n        teamMembers: \"Team Members\",\n        addMember: \"Add Member\",\n        improveWithAI: \"Improve with AI\",\n        currentDescription: \"Current Description\",\n        noDescriptionYet: \"No description added yet.\",\n        generating: \"Generating...\",\n        generateImprovedDescription: \"Generate Improved Description\",\n        aiSuggestion: \"AI Suggestion\",\n        aiSuggestionApplied: \"AI Suggestion Applied\",\n        descriptionUpdatedWithAI: \"Description updated with AI suggestion.\",\n        applyAISuggestion: \"Apply AI Suggestion\",\n        aiProjectPromptPlaceholder: \"e.g. Ask about project improvements, task suggestions, etc.\" // Added missing key\n    },\n    ru: {\n        // General\n        dashboard: \"Панель управления\",\n        projects: \"Проекты\",\n        analytics: \"Аналитика\",\n        settings: \"Настройки\",\n        tasks: \"Задачи\",\n        documents: \"Документы\",\n        save: \"Сохранить\",\n        cancel: \"Отмена\",\n        calendar: \"Календарь\",\n        // Tasks\n        toDo: \"К выполнению\",\n        inProgress: \"В процессе\",\n        done: \"Выполнено\",\n        addTask: \"Добавить задачу\",\n        deleteTask: \"Удалить задачу\",\n        editTask: \"Редактировать задачу\",\n        taskTitle: \"Название задачи\",\n        taskDescription: \"Описание задачи\",\n        taskPriority: \"Приоритет\",\n        high: \"Высокий\",\n        medium: \"Средний\",\n        low: \"Низкий\",\n        dueDate: \"Срок выполнения\",\n        selectPriority: \"Выберите приоритет\",\n        taskAge: \"Возраст задачи\",\n        daysLeft: \" дней осталось\",\n        daysOverdue: \" дней просрочено\",\n        taskUpdated: \"Задача обновлена\",\n        taskUpdatedSuccess: \"Задача успешно обновлена\",\n        taskDeleted: \"Задача удалена\",\n        taskDeletedSuccess: \"Задача успешно удалена\",\n        taskCreated: \"Задача создана\",\n        taskCreatedSuccess: \"Задача успешно создана\",\n        deleteTaskConfirmation: \"Вы уверены, что хотите удалить задачу {title}?\",\n        delete: \"Удалить\",\n        close: \"Закрыть\",\n        editTaskDescription: \"Редактирование деталей задачи\",\n        viewMode: \"Режим просмотра\",\n        kanban: \"Канбан\",\n        list: \"Список\",\n        searchTasks: \"Поиск задач\",\n        priority: \"Приоритет\",\n        allPriorities: \"Все приоритеты\",\n        assignee: \"Исполнитель\",\n        allAssignees: \"Все исполнители\",\n        noTasksFound: \"Задачи не найдены\",\n        add: \"Добавить\",\n        commaSeparatedTags: \"Теги, разделенные запятыми\",\n        // Subtasks\n        manageSubtasks: \"Управление подзадачами\",\n        manageSubtasksDescription: \"Добавление или управление подзадачами\",\n        subtasks: \"Подзадачи\",\n        newSubtask: \"Новая подзадача\",\n        newSubtaskPlaceholder: \"Введите название подзадачи\",\n        extractSubtasks: \"Извлечь как подзадачи\",\n        noSubtasksFound: \"Подзадачи не найдены\",\n        noSubtasksFoundDescription: \"Не удалось извлечь подзадачи из ответа ИИ\",\n        subtasksAdded: \"Подзадачи добавлены\",\n        subtasksAddedFromAI: \"Добавлено {count} подзадач из предложения ИИ\",\n        generateSubtasksWithAI: \"Создать подзадачи с помощью ИИ\",\n        // AI Assistant\n        aiAssist: \"ИИ-помощник\",\n        aiAssistDescription: \"Получить помощь ИИ для этой задачи\",\n        aiPrompt: \"Спросить ИИ\",\n        aiPromptPlaceholder: \"например: Помоги разбить эту задачу на подзадачи\",\n        aiPromptExample1: \"Разбей эту задачу\",\n        aiPromptExample2: \"Предложи приоритет\",\n        aiPromptExample3: \"Рекомендуй следующие шаги\",\n        aiResponse: \"Ответ ИИ\",\n        generateResponse: \"Сгенерировать\",\n        thinking: \"Думаю...\",\n        currentTask: \"Текущая задача\",\n        // Projects\n        projectProgress: \"Прогресс проекта\",\n        createProject: \"Создать проект\",\n        editProject: \"Редактировать проект\",\n        deleteProject: \"Удалить проект\",\n        projectName: \"Название проекта\",\n        projectDescription: \"Описание проекта\",\n        selectProject: \"Выбрать проект\",\n        newProject: \"Новый проект\",\n        projectCreated: \"Проект создан\",\n        projectCreatedDescription: \"Проект успешно создан\",\n        projectUpdated: \"Проект обновлен\",\n        projectUpdatedDescription: \"Проект успешно обновлен\",\n        projectDeleted: \"Проект удален\",\n        projectDeletedDescription: \"Проект успешно удален\",\n        deleteProjectConfirmation: \"Вы уверены, что хотите удалить проект {name}?\",\n        projectCompletion: \"Завершенность проекта\",\n        aiProjectAssistant: \"ИИ-помощник проекта\",\n        askAI: \"Спросить ИИ\",\n        applyAISuggestions: \"Применить предложения ИИ\",\n        aiSuggestionsApplied: \"Предложения ИИ применены\",\n        projectUpdatedWithAI: \"Проект обновлен с учетом предложений ИИ\",\n        privateProject: \"Приватный проект\",\n        privateProjectDescription: \"Приватные проекты видны только вам и людям, с которыми вы ими делитесь\",\n        private: \"Приватный\",\n        shared: \"Общедоступный\",\n        shareProject: \"Поделиться проектом\",\n        currentParticipants: \"Текущие участники\",\n        addParticipants: \"Добавить участников\",\n        searchTeamMembers: \"Поиск участников команды\",\n        noResultsFound: \"Результаты не найдены\",\n        selectedParticipants: \"Выбранные участники\",\n        share: \"Поделиться\",\n        projectShared: \"Проект доступен для совместной работы\",\n        projectSharedDescription: \"Проект доступен для {count} участников\",\n        participantRemoved: \"Участник удален\",\n        participantRemovedDescription: \"Участник удален из проекта\",\n        totalProjects: \"Всего проектов\",\n        completedProjects: \"Завершенных проектов\",\n        inProgressProjects: \"В процессе\",\n        averageCompletion: \"Средняя завершенность\",\n        noProjects: \"Нет проектов\",\n        createFirstProject: \"Создайте свой первый проект, чтобы начать управление задачами\",\n        participants: \"Участники\",\n        noParticipants: \"Нет участников\",\n        actions: \"Действия\",\n        completion: \"Завершенность\",\n        createdAt: \"Создан\",\n        // Documents\n        projectDocuments: \"Документы проекта\",\n        uploadDocument: \"Загрузить документ\",\n        addDocumentLink: \"Добавить ссылку на документ\",\n        viewDocuments: \"Просмотр документов\",\n        noDocuments: \"Нет документов для этого проекта\",\n        documentName: \"Название документа\",\n        documentType: \"Тип документа\",\n        documentURL: \"URL документа\",\n        addDocument: \"Добавить документ\",\n        addDocumentDescription: \"Добавить ссылку на документ к текущему проекту\",\n        documentUploaded: \"Документ загружен\",\n        documentUploadedSuccess: \"Документ {name} успешно загружен\",\n        documentAdded: \"Документ добавлен\",\n        documentAddedSuccess: \"Документ {name} успешно добавлен\",\n        editDocument: \"Редактировать документ\",\n        deleteDocument: \"Удалить документ\",\n        deleteDocumentConfirmation: \"Вы уверены, что хотите удалить документ {name}?\",\n        documentDeleted: \"Документ удален\",\n        documentDeletedSuccessfully: \"Документ успешно удален\",\n        documentUpdated: \"Документ обновлен\",\n        documentUpdatedSuccessfully: \"Документ {name} успешно обновлен\",\n        folders: \"Папки\",\n        files: \"Файлы\",\n        newFolder: \"Новая папка\",\n        uploadFiles: \"Загрузить файлы\",\n        uploadFilesDescription: \"Загрузка файлов в текущий проект\",\n        dropFilesHere: \"Перетащите файлы сюда\",\n        or: \"или\",\n        browseFiles: \"Выбрать файлы\",\n        selectedFiles: \"Выбранные файлы\",\n        uploading: \"Загрузка...\",\n        upload: \"Загрузить\",\n        uploadComplete: \"Загрузка завершена\",\n        filesUploadedSuccessfully: \"{count} файлов успешно загружено\",\n        createdBy: \"Создал\",\n        lastModified: \"Последнее изменение\",\n        size: \"Размер\",\n        modified: \"Изменен\",\n        folder: \"Папка\",\n        noFolder: \"Без папки\",\n        tags: \"Теги\",\n        content: \"Содержимое\",\n        aiDocumentAssistant: \"ИИ-помощник для документов\",\n        aiDocumentPromptPlaceholder: \"например: Улучши этот документ, Сделай резюме, Предложи теги\",\n        aiDocumentPromptSuggestion1: \"Улучшить документ\",\n        aiDocumentPromptSuggestion2: \"Сделать резюме\",\n        aiDocumentPromptSuggestion3: \"Предложить теги\",\n        currentDocument: \"Текущий документ\",\n        documentUpdatedWithAI: \"Документ обновлен с учетом предложений ИИ\",\n        documentAIUpdated: \"Документ {name} обновлен с учетом предложений ИИ\",\n        openMenu: \"Открыть меню\",\n        view: \"Просмотр\",\n        edit: \"Редактировать\",\n        download: \"Скачать\",\n        totalDocuments: \"Всего документов\",\n        totalSize: \"Общий размер\",\n        latestUpdate: \"Последнее обновление\",\n        documentTypes: \"Типы документов\",\n        groupByType: \"Группировать по типу\",\n        version: \"Версия\",\n        status: \"Статус\",\n        description: \"Описание\",\n        // Admin\n        adminDashboard: \"Панель администратора\",\n        userManagement: \"Управление пользователями\",\n        blockUser: \"Заблокировать пользователя\",\n        unblockUser: \"Разблокировать пользователя\",\n        deleteUser: \"Удалить пользователя\",\n        confirmDelete: \"Вы уверены, что хотите удалить этого пользователя?\",\n        confirmBlock: \"Вы уверены, что хотите заблокировать этого пользователя?\",\n        confirmUnblock: \"Вы уверены, что хотите разблокировать этого пользователя?\",\n        username: \"Имя пользователя\",\n        email: \"Email\",\n        active: \"Активен\",\n        blocked: \"Заблокирован\",\n        // Settings\n        settingsSaved: \"Настройки сохранены\",\n        generalSettingsSaved: \"Общие настройки успешно сохранены\",\n        aiSettingsSaved: \"Настройки ИИ успешно сохранены\",\n        emailSettingsSaved: \"Настройки электронной почты успешно сохранены\",\n        aiModelApiKey: \"API-ключ модели ИИ\",\n        enterApiKeyPlaceholder: \"Введите ваш API-ключ\",\n        // Admin Settings specific keys\n        generalSettings: \"Общие настройки\",\n        aiSettings: \"Настройки ИИ\",\n        emailSettings: \"Настройки Email\",\n        generalSettingsDescription: \"Настройка общих параметров сайта\",\n        aiSettingsDescription: \"Настройка параметров ИИ-помощника\",\n        emailSettingsDescription: \"Настройка параметров уведомлений по email\",\n        siteName: \"Название сайта\",\n        siteDescription: \"Описание сайта\",\n        allowRegistration: \"Разрешить регистрацию пользователей\",\n        requireEmailVerification: \"Требовать подтверждение Email\",\n        maxProjectsPerUser: \"Макс. проектов на пользователя\",\n        maxTasksPerProject: \"Макс. задач на проект\",\n        saveSettings: \"Сохранить настройки\",\n        enableAI: \"Включить ИИ-помощника\",\n        aiModel: \"Модель ИИ\",\n        aiTemperature: \"Температура ИИ\",\n        maxTokens: \"Макс. токенов\",\n        customPrompt: \"Пользовательский системный промпт\",\n        aiWarning: \"Предупреждение об использовании ИИ\",\n        aiWarningDescription: \"Информировать пользователей о возможных неточностях ИИ или затратах\",\n        enableEmailNotifications: \"Включить уведомления по Email\",\n        smtpServer: \"SMTP Сервер\",\n        smtpPort: \"SMTP Порт\",\n        smtpUsername: \"SMTP Имя пользователя\",\n        smtpPassword: \"SMTP Пароль\",\n        fromEmail: \"Email отправителя\",\n        systemSettings: \"Системные настройки\",\n        // Admin Dashboard specific keys\n        adminDashboardDescription: \"Обзор статистики и управления системой\",\n        totalUsers: \"Всего пользователей\",\n        activeUsers: \"Активных пользователей\",\n        totalTasks: \"Всего задач\",\n        completedTasks: \"Завершенных задач\",\n        completionRate: \"Уровень завершения\",\n        taskCompletionRate: \"Уровень завершения задач\",\n        // Dashboard specific keys\n        logoutSuccessful: \"Выход выполнен успешно\",\n        youHaveBeenLoggedOut: \"Вы успешно вышли из системы.\",\n        // Analytics specific keys\n        taskDistribution: \"Распределение задач\",\n        projectStatistics: \"Статистика проекта\",\n        tasksByStatus: \"Задачи по статусу\",\n        tasksByStatusDescription: \"Распределение задач по различным статусам\",\n        taskCompletion: \"Завершение задач со временем\",\n        taskCompletionDescription: \"Тренд завершения задач\",\n        completed: \"Завершено\",\n        projectsByStatus: \"Проекты по статусу\",\n        projectsByStatusDescription: \"Распределение проектов по различным статусам\",\n        onHold: \"На удержании\",\n        monthlyProjects: \"Создание проектов по месяцам\",\n        monthlyProjectsDescription: \"Тренд создания новых проектов каждый месяц\",\n        // User Analytics specific keys\n        userParticipation: \"Участие пользователей\",\n        userActivity: \"Активность пользователей\",\n        projectCollaboration: \"Совместная работа\",\n        topUsersByProjects: \"Топ пользователей по проектам\",\n        topUsersByProjectsDescription: \"Пользователи с наибольшим количеством проектов\",\n        projectOwnershipDistribution: \"Распределение владения проектами\",\n        projectOwnershipDistributionDescription: \"Кто владеет большинством проектов\",\n        topUsersByTasks: \"Топ пользователей по задачам\",\n        topUsersByTasksDescription: \"Пользователи с наибольшим количеством задач\",\n        taskCompletionByUser: \"Выполнение задач по пользователям\",\n        taskCompletionByUserDescription: \"Кто выполняет больше всего задач\",\n        projectsByTeamSize: \"Проекты по размеру команды\",\n        projectsByTeamSizeDescription: \"Распределение проектов по количеству участников\",\n        collaborationDistribution: \"Распределение совместной работы\",\n        collaborationDistributionDescription: \"Как распределены проекты по количеству участников\",\n        totalProjects: \"Всего проектов\",\n        ownedProjects: \"Владеет проектами\",\n        participatedProjects: \"Участвует в проектах\",\n        completedTasks: \"Завершенные задачи\",\n        inProgressTasks: \"Задачи в процессе\",\n        todoTasks: \"Задачи в очереди\",\n        singleUserProjects: \"Проекты с 1 участником\",\n        multiUserProjects: \"Проекты с {count} участниками\",\n        // Sidebar specific keys\n        aiAssistant: \"ИИ-помощник\",\n        aiTaskTracker: \"ИИ Трекер Задач\",\n        // Project timeline keys\n        projectAge: \"Возраст проекта\",\n        daysLeft: \"Осталось дней\",\n        days: \"дней\",\n        daysOverdue: \"дней просрочено\",\n        noDueDate: \"Нет срока\",\n        noChanges: \"Без изменений\",\n        allParticipantsAlreadyAdded: \"Все выбранные участники уже добавлены в проект\",\n        // AI Assistant\n        error: \"Ошибка\",\n        aiRequestFailed: \"Не удалось обработать запрос к ИИ. Пожалуйста, попробуйте еще раз.\",\n        // Other\n        all: \"Все\",\n        spreadsheets: \"Таблицы\",\n        useMainMenu: \"Используйте боковое меню для навигации\",\n        pdfs: \"PDF-документы\",\n        other: \"Другое\",\n        noDocumentsFound: \"Документы не найдены\",\n        noDocumentsMatchingSearch: \"Нет документов, соответствующих запросу '{query}'\",\n        noDocumentsInProject: \"В этом проекте нет документов\",\n        noProjectSelected: \"Проект не выбран\",\n        selectProjectToViewDocuments: \"Выберите проект для просмотра документов\",\n        selectProjectToManageDocuments: \"Выберите проект для управления документами\",\n        documentsForProject: \"Документы проекта: {name}\",\n        searchDocuments: \"Поиск документов\",\n        // Header\n        myAccount: \"Мой аккаунт\",\n        profile: \"Профиль\",\n        headerSettings: \"Настройки\",\n        adminPanel: \"Панель администратора\",\n        adminRole: \"Администратор\",\n        userRole: \"Пользователь\",\n        logout: \"Выйти\",\n        notifications: \"Уведомления\",\n        clearAll: \"Очистить все\",\n        noNotifications: \"Нет уведомлений\",\n        role: \"Роль\",\n        name: \"Имя\",\n        editProfile: \"Редактировать профиль\",\n        appearance: \"Внешний вид\",\n        theme: \"Тема\",\n        language: \"Язык\",\n        light: \"Светлая\",\n        dark: \"Темная\",\n        marketingEmails: \"Маркетинговые рассылки\",\n        taskNotifications: \"Уведомления о задачах\",\n        systemNotifications: \"Системные уведомления\",\n        projectNotifications: \"Уведомления о проектах\",\n        reminderSettings: \"Настройки напоминаний\",\n        emailReminders: \"Напоминания на почту\",\n        telegramReminders: \"Напоминания в Telegram\",\n        reminders: \"Напоминания\",\n        emailRemindersDescription: \"Получать напоминания на электронную почту\",\n        telegramRemindersDescription: \"Получать напоминания в Telegram\",\n        emailForReminders: \"Email для напоминаний\",\n        telegramUsername: \"Имя пользователя в Telegram\",\n        emailFromProfileUsed: \"Используется email из вашего профиля\",\n        reminderSettingsSaved: \"Настройки напоминаний успешно обновлены\",\n        saveChanges: \"Сохранить изменения\",\n        settingsUpdatedSuccess: \"Настройки успешно обновлены\",\n        // Project Management Specific\n        validationError: \"Ошибка валидации\",\n        projectNameRequired: \"Название проекта обязательно.\",\n        selectProjectToUseQuickActions: \"Выберите проект для использования быстрых действий.\",\n        error: \"Ошибка\",\n        cannotNavigateWithoutProject: \"Невозможно перейти к задачам без выбранного проекта.\",\n        createNewProject: \"Создать новый проект\",\n        projectNamePlaceholder: \"Введите название проекта\",\n        projectDescriptionPlaceholder: \"Введите описание проекта\",\n        selectAssignee: \"Выберите исполнителя\",\n        unassigned: \"Не назначен\",\n        selectDate: \"Выберите дату\",\n        create: \"Создать\",\n        viewTasks: \"Просмотр задач\",\n        discussProject: \"Обсудить проект\",\n        discussTask: \"Обсудить задачу\",\n        projectDetails: \"Детали проекта\",\n        // status: \"Статус\", // Removed duplicate - already exists under // Documents\n        created: \"Создан\",\n        notSet: \"Не указан\",\n        privacy: \"Приватность\",\n        public: \"Публичный\",\n        todo: \"К выполнению\",\n        quickActions: \"Быстрые действия\",\n        viewCalendar: \"Просмотр календаря\",\n        team: \"Команда\",\n        teamMembers: \"Участники команды\",\n        addMember: \"Добавить участника\",\n        improveWithAI: \"Улучшить с помощью ИИ\",\n        currentDescription: \"Текущее описание\",\n        noDescriptionYet: \"Описание еще не добавлено.\",\n        generating: \"Генерация...\",\n        generateImprovedDescription: \"Сгенерировать улучшенное описание\",\n        aiSuggestion: \"Предложение ИИ\",\n        aiSuggestionApplied: \"Предложение ИИ применено\",\n        descriptionUpdatedWithAI: \"Описание обновлено с помощью предложения ИИ.\",\n        applyAISuggestion: \"Применить предложение ИИ\",\n        aiProjectPromptPlaceholder: \"например: Спроси об улучшениях проекта, предложениях задач и т.д.\" // Added missing key\n    }\n};\n// Create the context\n// Adjust the context definition to match the provider's t function signature\nconst TranslationContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    t: (key, params = {})=>key,\n    locale: \"en\",\n    setLocale: ()=>{}\n});\nfunction TranslationProvider({ children }) {\n    const [locale, setLocale] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"en\");\n    // Translation function\n    // Use TranslationKey for the key parameter\n    const t = (key, params = {})=>{\n        const currentLocale = locale; // Cast locale\n        // Now TS knows 'key' is a valid key for languages[currentLocale]\n        const translation = languages[currentLocale]?.[key] || key;\n        // Replace parameters in the translation\n        if (params && Object.keys(params).length > 0) {\n            return Object.keys(params).reduce((acc, paramKey)=>{\n                return acc.replace(`{${paramKey}}`, params[paramKey]);\n            }, translation);\n        }\n        return translation;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TranslationContext.Provider, {\n        value: {\n            t,\n            locale,\n            setLocale\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\task-ai-manager3\\\\lib\\\\translations.tsx\",\n        lineNumber: 841,\n        columnNumber: 10\n    }, this);\n}\n// Custom hook to use the translation context\nfunction useTranslation() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(TranslationContext);\n    if (context === undefined) {\n        throw new Error(\"useTranslation must be used within a TranslationProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/translations.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Clib%5C%5Csocket-context.tsx%22%2C%22ids%22%3A%5B%22SocketProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Clib%5C%5Ctranslations.tsx%22%2C%22ids%22%3A%5B%22TranslationProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%2C%5C%22cyrillic%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-jetbrains%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Clib%5C%5Csocket-context.tsx%22%2C%22ids%22%3A%5B%22SocketProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Clib%5C%5Ctranslations.tsx%22%2C%22ids%22%3A%5B%22TranslationProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%2C%5C%22cyrillic%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-jetbrains%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(ssr)/./components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./lib/socket-context.tsx */ \"(ssr)/./lib/socket-context.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./lib/translations.tsx */ \"(ssr)/./lib/translations.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0cyU1QyU1Q3Rhc2stYWktbWFuYWdlcjMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDUHJvamVjdHMlNUMlNUN0YXNrLWFpLW1hbmFnZXIzJTVDJTVDY29tcG9uZW50cyU1QyU1Q3RoZW1lLXByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlRoZW1lUHJvdmlkZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q1Byb2plY3RzJTVDJTVDdGFzay1haS1tYW5hZ2VyMyU1QyU1Q2xpYiU1QyU1Q3NvY2tldC1jb250ZXh0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlNvY2tldFByb3ZpZGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0cyU1QyU1Q3Rhc2stYWktbWFuYWdlcjMlNUMlNUNsaWIlNUMlNUN0cmFuc2xhdGlvbnMudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyVHJhbnNsYXRpb25Qcm92aWRlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDUHJvamVjdHMlNUMlNUN0YXNrLWFpLW1hbmFnZXIzJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2ZvbnQlNUMlNUNnb29nbGUlNUMlNUN0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMmFwcCU1QyU1QyU1QyU1Q2xheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiUyQyU1QyUyMmN5cmlsbGljJTVDJTIyJTVEJTJDJTVDJTIydmFyaWFibGUlNUMlMjIlM0ElNUMlMjItLWZvbnQtaW50ZXIlNUMlMjIlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDUHJvamVjdHMlNUMlNUN0YXNrLWFpLW1hbmFnZXIzJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2ZvbnQlNUMlNUNnb29nbGUlNUMlNUN0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMmFwcCU1QyU1QyU1QyU1Q2xheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJKZXRCcmFpbnNfTW9ubyU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCUyQyU1QyUyMnZhcmlhYmxlJTVDJTIyJTNBJTVDJTIyLS1mb250LWpldGJyYWlucyU1QyUyMiU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmpldGJyYWluc01vbm8lNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBLQUFzSTtBQUN0STtBQUNBLDRKQUFnSTtBQUNoSTtBQUNBLHdKQUFtSSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiVGhlbWVQcm92aWRlclwiXSAqLyBcIkQ6XFxcXFByb2plY3RzXFxcXHRhc2stYWktbWFuYWdlcjNcXFxcY29tcG9uZW50c1xcXFx0aGVtZS1wcm92aWRlci50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlNvY2tldFByb3ZpZGVyXCJdICovIFwiRDpcXFxcUHJvamVjdHNcXFxcdGFzay1haS1tYW5hZ2VyM1xcXFxsaWJcXFxcc29ja2V0LWNvbnRleHQudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJUcmFuc2xhdGlvblByb3ZpZGVyXCJdICovIFwiRDpcXFxcUHJvamVjdHNcXFxcdGFzay1haS1tYW5hZ2VyM1xcXFxsaWJcXFxcdHJhbnNsYXRpb25zLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Clib%5C%5Csocket-context.tsx%22%2C%22ids%22%3A%5B%22SocketProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Clib%5C%5Ctranslations.tsx%22%2C%22ids%22%3A%5B%22TranslationProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%2C%5C%22cyrillic%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-jetbrains%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ws","vendor-chunks/@opentelemetry","vendor-chunks/engine.io-client","vendor-chunks/socket.io-client","vendor-chunks/xmlhttprequest-ssl","vendor-chunks/debug","vendor-chunks/socket.io-parser","vendor-chunks/engine.io-parser","vendor-chunks/next-themes","vendor-chunks/@socket.io","vendor-chunks/supports-color","vendor-chunks/ms","vendor-chunks/@swc","vendor-chunks/has-flag"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5CProjects%5Ctask-ai-manager3%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjects%5Ctask-ai-manager3&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();