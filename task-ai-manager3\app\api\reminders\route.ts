"use server";

import { NextRequest, NextResponse } from 'next/server';
import { checkAndSendReminders } from '@/lib/reminder-service';
import { processCalendarReminders } from '@/lib/calendar-reminder-service';
import { processTaskReminders } from '@/lib/task-reminder-service';
import { prisma } from '@/lib/prisma';

// Обработчик GET-запроса для проверки и отправки напоминаний
export async function GET(request: NextRequest) {
  try {
    // Проверяем и отправляем напоминания
    await checkAndSendReminders();
    
    return NextResponse.json({ success: true, message: 'Reminders checked and sent' });
  } catch (error) {
    console.error('Error in reminders API:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to check and send reminders', error: String(error) },
      { status: 500 }
    );
  }
}

// Обработчик POST-запроса для создания напоминаний для календаря
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Проверяем наличие необходимых данных
    if (!body.userId || !body.events) {
      return NextResponse.json(
        { success: false, message: 'Missing required fields: userId or events' },
        { status: 400 }
      );
    }
    
    // Обрабатываем напоминания для календаря
    await processCalendarReminders(body.events, body.userId);
    
    return NextResponse.json({ success: true, message: 'Calendar reminders processed' });
  } catch (error) {
    console.error('Error in calendar reminders API:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to process calendar reminders', error: String(error) },
      { status: 500 }
    );
  }
}

// Обработчик PUT-запроса для создания напоминаний для задач
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Проверяем наличие необходимых данных
    if (!body.tasks) {
      return NextResponse.json(
        { success: false, message: 'Missing required field: tasks' },
        { status: 400 }
      );
    }
    
    // Обрабатываем напоминания для задач
    await processTaskReminders(body.tasks);
    
    return NextResponse.json({ success: true, message: 'Task reminders processed' });
  } catch (error) {
    console.error('Error in task reminders API:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to process task reminders', error: String(error) },
      { status: 500 }
    );
  }
}
