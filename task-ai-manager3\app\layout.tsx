import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter, JetBrains_Mono } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"
import { TranslationProvider } from "@/lib/translations"
import { SocketProvider } from "@/lib/socket-context" // Import SocketProvider

const inter = Inter({
  subsets: ["latin", "cyrillic"],
  variable: "--font-inter",
})

const jetbrainsMono = JetBrains_Mono({
  subsets: ["latin"],
  variable: "--font-jetbrains",
})

export const metadata: Metadata = {
  title: "Minimumist - AI Task Manager",
  description: "Minimalist task management app with AI assistant",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.variable} ${jetbrainsMono.variable} font-sans`}>
        {/* Wrap ThemeProvider (and thus children) with SocketProvider */}
        <SocketProvider>
          <TranslationProvider>
            <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
              {children}
            </ThemeProvider>
          </TranslationProvider>
        </SocketProvider>
      </body>
    </html>
  )
}
