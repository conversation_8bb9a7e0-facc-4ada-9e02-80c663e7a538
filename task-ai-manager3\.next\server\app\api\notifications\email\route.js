/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/notifications/email/route";
exports.ids = ["app/api/notifications/email/route"];
exports.modules = {

/***/ "(rsc)/./app/api/notifications/email/route.ts":
/*!**********************************************!*\
  !*** ./app/api/notifications/email/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n// Обработчик POST-запроса для отправки email-уведомлений\nasync function POST(request) {\n    try {\n        // Получаем данные из запроса\n        const data = await request.json();\n        // Проверяем наличие необходимых данных\n        if (!data.userEmail || !data.subject || !data.message) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Missing required fields'\n            }, {\n                status: 400\n            });\n        }\n        // В реальном приложении здесь была бы отправка email через SMTP\n        // Для демонстрации просто логируем данные\n        console.log('Email notification data:', data);\n        // Имитируем успешную отправку\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: `Email notification sent to ${data.userEmail}`\n        });\n    /* \n    // Пример реальной отправки email через nodemailer\n    const transporter = nodemailer.createTransport({\n      host: process.env.SMTP_SERVER,\n      port: parseInt(process.env.SMTP_PORT || '587'),\n      secure: process.env.SMTP_PORT === '465',\n      auth: {\n        user: process.env.SMTP_USERNAME,\n        pass: process.env.SMTP_PASSWORD,\n      },\n    });\n    \n    const mailOptions = {\n      from: `\"AI Task Manager\" <${process.env.FROM_EMAIL}>`,\n      to: data.userEmail,\n      subject: data.subject,\n      html: `\n        <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n          <h2 style=\"color: #333;\">${data.subject}</h2>\n          <p>Здравствуйте, ${data.userName || 'пользователь'}!</p>\n          <p>${data.message}</p>\n          <div style=\"background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;\">\n            <p><strong>Сообщение:</strong> ${data.messageText}</p>\n            <p><strong>От:</strong> ${data.senderName}</p>\n            <p><strong>Чат:</strong> ${data.chatName}</p>\n          </div>\n          <p>С уважением,<br>Команда AI Task Manager</p>\n        </div>\n      `,\n    };\n    \n    const info = await transporter.sendMail(mailOptions);\n    \n    return NextResponse.json({ \n      success: true, \n      message: `Email notification sent to ${data.userEmail}`,\n      messageId: info.messageId\n    });\n    */ } catch (error) {\n        console.error('Error sending email notification:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'Failed to send email notification',\n            error: String(error)\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/notifications/email/route.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnotifications%2Femail%2Froute&page=%2Fapi%2Fnotifications%2Femail%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnotifications%2Femail%2Froute.ts&appDir=D%3A%5CProjects%5Ctask-ai-manager3%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjects%5Ctask-ai-manager3&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnotifications%2Femail%2Froute&page=%2Fapi%2Fnotifications%2Femail%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnotifications%2Femail%2Froute.ts&appDir=D%3A%5CProjects%5Ctask-ai-manager3%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjects%5Ctask-ai-manager3&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Projects_task_ai_manager3_app_api_notifications_email_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/notifications/email/route.ts */ \"(rsc)/./app/api/notifications/email/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/notifications/email/route\",\n        pathname: \"/api/notifications/email\",\n        filename: \"route\",\n        bundlePath: \"app/api/notifications/email/route\"\n    },\n    resolvedPagePath: \"D:\\\\Projects\\\\task-ai-manager3\\\\app\\\\api\\\\notifications\\\\email\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Projects_task_ai_manager3_app_api_notifications_email_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnotifications%2Femail%2Froute&page=%2Fapi%2Fnotifications%2Femail%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnotifications%2Femail%2Froute.ts&appDir=D%3A%5CProjects%5Ctask-ai-manager3%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjects%5Ctask-ai-manager3&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnotifications%2Femail%2Froute&page=%2Fapi%2Fnotifications%2Femail%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnotifications%2Femail%2Froute.ts&appDir=D%3A%5CProjects%5Ctask-ai-manager3%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjects%5Ctask-ai-manager3&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();