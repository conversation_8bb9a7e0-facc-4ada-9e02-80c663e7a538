import { NextRequest, NextResponse } from 'next/server';
import nodemailer from 'nodemailer';

// Обработчик POST-запроса для отправки email-уведомлений
export async function POST(request: NextRequest) {
  try {
    // Получаем данные из запроса
    const data = await request.json();
    
    // Проверяем наличие необходимых данных
    if (!data.userEmail || !data.subject || !data.message) {
      return NextResponse.json(
        { success: false, message: 'Missing required fields' },
        { status: 400 }
      );
    }
    
    // В реальном приложении здесь была бы отправка email через SMTP
    // Для демонстрации просто логируем данные
    console.log('Email notification data:', data);
    
    // Имитируем успешную отправку
    return NextResponse.json({ 
      success: true, 
      message: `Email notification sent to ${data.userEmail}` 
    });
    
    /* 
    // Пример реальной отправки email через nodemailer
    const transporter = nodemailer.createTransport({
      host: process.env.SMTP_SERVER,
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_PORT === '465',
      auth: {
        user: process.env.SMTP_USERNAME,
        pass: process.env.SMTP_PASSWORD,
      },
    });
    
    const mailOptions = {
      from: `"AI Task Manager" <${process.env.FROM_EMAIL}>`,
      to: data.userEmail,
      subject: data.subject,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">${data.subject}</h2>
          <p>Здравствуйте, ${data.userName || 'пользователь'}!</p>
          <p>${data.message}</p>
          <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <p><strong>Сообщение:</strong> ${data.messageText}</p>
            <p><strong>От:</strong> ${data.senderName}</p>
            <p><strong>Чат:</strong> ${data.chatName}</p>
          </div>
          <p>С уважением,<br>Команда AI Task Manager</p>
        </div>
      `,
    };
    
    const info = await transporter.sendMail(mailOptions);
    
    return NextResponse.json({ 
      success: true, 
      message: `Email notification sent to ${data.userEmail}`,
      messageId: info.messageId
    });
    */
    
  } catch (error) {
    console.error('Error sending email notification:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to send email notification', error: String(error) },
      { status: 500 }
    );
  }
}
