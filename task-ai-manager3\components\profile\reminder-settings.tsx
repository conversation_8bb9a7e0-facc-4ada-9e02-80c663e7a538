"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/use-toast";
import { Save, Bell } from "lucide-react";
import { useTranslation } from '@/lib/translations';

interface ReminderSettingsProps {
  userId: string;
  userEmail?: string;
}

export default function ReminderSettings({ userId, userEmail }: ReminderSettingsProps) {
  const { t } = useTranslation();
  const { toast } = useToast();
  
  // Состояния для настроек напоминаний
  const [emailReminders, setEmailReminders] = useState(false);
  const [telegramReminders, setTelegramReminders] = useState(false);
  const [emailForReminders, setEmailForReminders] = useState(userEmail || '');
  const [telegramUsername, setTelegramUsername] = useState('');
  
  // Загружаем настройки из localStorage при монтировании компонента
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedEmailReminders = localStorage.getItem('emailReminders');
      if (savedEmailReminders !== null) {
        setEmailReminders(savedEmailReminders === 'true');
      }
      
      const savedTelegramReminders = localStorage.getItem('telegramReminders');
      if (savedTelegramReminders !== null) {
        setTelegramReminders(savedTelegramReminders === 'true');
      }
      
      const savedEmailForReminders = localStorage.getItem('emailForReminders');
      if (savedEmailForReminders !== null) {
        setEmailForReminders(savedEmailForReminders);
      } else if (userEmail) {
        setEmailForReminders(userEmail);
      }
      
      const savedTelegramUsername = localStorage.getItem('telegramUsername');
      if (savedTelegramUsername !== null) {
        setTelegramUsername(savedTelegramUsername);
      }
    }
  }, [userEmail]);
  
  // Функция для сохранения настроек
  const handleSaveSettings = () => {
    // Сохраняем настройки в localStorage
    localStorage.setItem('emailReminders', String(emailReminders));
    localStorage.setItem('telegramReminders', String(telegramReminders));
    localStorage.setItem('emailForReminders', emailForReminders);
    localStorage.setItem('telegramUsername', telegramUsername);
    
    // В реальном приложении здесь был бы запрос к API для сохранения настроек в базе данных
    
    // Показываем уведомление об успешном сохранении
    toast({
      title: t("settingsSaved") || "Настройки сохранены",
      description: t("reminderSettingsSaved") || "Настройки напоминаний успешно обновлены",
    });
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Bell className="mr-2 h-5 w-5" />
          {t("reminderSettings") || "Настройки напоминаний"}
        </CardTitle>
        <CardDescription>
          {t("reminderSettingsDescription") || "Настройте способы получения напоминаний о задачах и событиях"}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="email-reminders" className="text-base">
                {t("emailReminders") || "Напоминания на почту"}
              </Label>
              <p className="text-sm text-muted-foreground">
                {t("emailRemindersDescription") || "Получать напоминания на электронную почту"}
              </p>
            </div>
            <Switch
              id="email-reminders"
              checked={emailReminders}
              onCheckedChange={setEmailReminders}
            />
          </div>
          
          {emailReminders && (
            <div className="ml-6 space-y-2">
              <Label htmlFor="email-for-reminders">
                {t("emailForReminders") || "Email для напоминаний"}
              </Label>
              <Input
                id="email-for-reminders"
                type="email"
                value={emailForReminders}
                onChange={(e) => setEmailForReminders(e.target.value)}
                placeholder="<EMAIL>"
              />
            </div>
          )}
        </div>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="telegram-reminders" className="text-base">
                {t("telegramReminders") || "Напоминания в Telegram"}
              </Label>
              <p className="text-sm text-muted-foreground">
                {t("telegramRemindersDescription") || "Получать напоминания в Telegram"}
              </p>
            </div>
            <Switch
              id="telegram-reminders"
              checked={telegramReminders}
              onCheckedChange={setTelegramReminders}
            />
          </div>
          
          {telegramReminders && (
            <div className="ml-6 space-y-2">
              <Label htmlFor="telegram-username">
                {t("telegramUsername") || "Имя пользователя в Telegram"}
              </Label>
              <Input
                id="telegram-username"
                value={telegramUsername}
                onChange={(e) => setTelegramUsername(e.target.value)}
                placeholder="@username"
              />
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter>
        <Button onClick={handleSaveSettings} className="ml-auto">
          <Save className="mr-2 h-4 w-4" />
          {t("saveSettings") || "Сохранить настройки"}
        </Button>
      </CardFooter>
    </Card>
  );
}
